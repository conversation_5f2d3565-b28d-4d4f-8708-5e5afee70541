"""
热门文章爬虫 - 专门爬取各个页面的热门文章
"""
import asyncio
import logging
import json
import argparse
import sys
import os
import time
from typing import List, Dict, Any
from pathlib import Path
from bs4 import BeautifulSoup

from crawler_engine import SmartCrawler, CrawlResult
from extractors import XueqiuHomePageData
from config import AntiDetectionConfig

# 导入自建的数据库模块
try:
    from database import get_database_manager, init_database
    DATABASE_AVAILABLE = True
except ImportError as e:
    print(f"数据库模块导入失败: {e}")
    DATABASE_AVAILABLE = False

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('crawler.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class PopularArticlesCrawler:
    """热门文章爬虫主类"""

    def __init__(self, enable_anti_detection: bool = True, save_to_database: bool = True, database_url: str = None):
        self.crawler = SmartCrawler(enable_anti_detection)
        self.save_to_database = save_to_database and DATABASE_AVAILABLE
        self.db_manager = None

        # 初始化数据库
        if self.save_to_database:
            try:
                if database_url:
                    success = init_database(database_url)
                else:
                    success = init_database()

                if success:
                    self.db_manager = get_database_manager()
                    logger.info("数据库保存功能已启用")
                else:
                    self.save_to_database = False
                    logger.warning("数据库初始化失败，禁用数据库保存功能")
            except Exception as e:
                logger.error(f"数据库初始化异常: {e}")
                self.save_to_database = False

        if not self.save_to_database:
            logger.info("数据库保存功能已禁用，仅保存到JSON文件")
        self.results = []
        
    async def crawl_popular_articles(self, pages: List[str] = None, enhanced_mode: bool = True, fetch_full_content: bool = True) -> List[Dict[str, Any]]:
        """爬取热门文章"""
        if pages is None:
            pages = [
                # "https://xueqiu.com",  # 雪球首页
                "https://xueqiu.com/today",  # 今日热门
                # "https://xueqiu.com/hq",  # 行情页面
            ]

        logger.info(f"开始爬取 {len(pages)} 个页面的热门文章")

        all_articles = []
        for page_url in pages:
            try:
                logger.info(f"正在爬取页面: {page_url}")
                articles = await self._crawl_page_articles(page_url, enhanced_mode)
                if articles:
                    all_articles.extend(articles)
                    logger.info(f"从 {page_url} 获取到 {len(articles)} 篇文章")
                else:
                    logger.warning(f"从 {page_url} 未获取到文章")
            except Exception as e:
                logger.error(f"爬取页面 {page_url} 失败: {e}")
                continue

        # 去重和排序
        unique_articles = self._deduplicate_articles(all_articles)
        logger.info(f"去重后共获取 {len(unique_articles)} 篇热门文章")

        # 获取完整文章内容
        if fetch_full_content:
            logger.info("开始获取文章完整内容...")
            unique_articles = await self._fetch_full_article_contents(unique_articles)

        # 保存到数据库
        if self.save_to_database and self.db_manager:
            logger.info("开始保存文章到数据库...")
            db_stats = self.db_manager.save_articles(unique_articles)
            logger.info(f"数据库保存统计: {db_stats}")

        return unique_articles

    async def deep_crawl_from_today(self, max_depth: int = 3, max_articles: int = 1000, fetch_full_content: bool = True, fast_mode: bool = False, load_more_clicks: int = 8) -> List[Dict[str, Any]]:
        """从今日热门页面开始深度爬取所有文章页面链接"""
        logger.info(f"开始从 https://xueqiu.com/today 深度爬取文章，最大深度: {max_depth}, 最大文章数: {max_articles}")

        all_articles = []
        visited_urls = set()
        article_urls_to_crawl = set()

        # 第一步：从今日热门页面提取所有文章链接（包括动态加载）
        logger.info("第一步：从今日热门页面提取文章链接（支持加载更多）...")

        # 首先尝试增强型提取（支持加载更多）
        initial_links = await self._extract_article_links_with_load_more("https://xueqiu.com/today", max_load_more=load_more_clicks)

        # 如果增强型提取失败或链接太少，使用备用方案
        if len(initial_links) < 20:
            logger.info("增强型提取链接较少，尝试备用方案...")
            backup_links = await self._extract_article_links_from_page("https://xueqiu.com/today")
            # 合并链接
            all_initial_links = set(initial_links)
            all_initial_links.update(backup_links)
            initial_links = list(all_initial_links)

        # 过滤出有效的文章页面链接
        for link in initial_links:
            if self.db_manager and self.db_manager._is_valid_url(link):
                article_urls_to_crawl.add(link)

        logger.info(f"从今日热门页面提取到 {len(article_urls_to_crawl)} 个有效文章链接")

        # 第二步：并发深度爬取每个文章页面
        current_depth = 0
        while current_depth < max_depth and article_urls_to_crawl and len(all_articles) < max_articles:
            current_depth += 1
            logger.info(f"开始第 {current_depth} 层深度爬取，待爬取链接: {len(article_urls_to_crawl)} 个")

            # 当前层要处理的URL - 增加批次大小
            batch_size = min(100, len(article_urls_to_crawl))  # 每批最多处理100个
            current_batch = list(article_urls_to_crawl)[:batch_size]
            article_urls_to_crawl = article_urls_to_crawl - set(current_batch)

            # 过滤已访问的URL
            current_batch = [url for url in current_batch if url not in visited_urls]

            if not current_batch:
                continue

            # 并发爬取当前批次
            logger.info(f"并发爬取 {len(current_batch)} 个文章...")
            batch_results = await self._crawl_articles_batch(current_batch, fetch_full_content, visited_urls, fast_mode)

            # 处理批次结果
            for article_data, related_links in batch_results:
                if article_data and len(all_articles) < max_articles:
                    all_articles.append(article_data)
                    logger.info(f"成功爬取文章: {article_data.get('title', '')[:50]}...")

                    # 添加相关链接到待爬取队列
                    if current_depth < max_depth and related_links:
                        for link in related_links:
                            if (link not in visited_urls and
                                self.db_manager and
                                self.db_manager._is_valid_url(link)):
                                article_urls_to_crawl.add(link)

            logger.info(f"第 {current_depth} 层完成，已获取 {len(all_articles)} 篇文章")

        logger.info(f"深度爬取完成，共获取 {len(all_articles)} 篇文章")

        # 保存到数据库
        if self.save_to_database and self.db_manager and all_articles:
            logger.info("开始保存深度爬取的文章到数据库...")
            db_stats = self.db_manager.save_articles(all_articles)
            logger.info(f"数据库保存统计: {db_stats}")

        return all_articles

    async def _crawl_articles_batch(self, urls: List[str], fetch_full_content: bool, visited_urls: set, fast_mode: bool = False) -> List[tuple]:
        """并发爬取一批文章"""
        # 根据模式调整并发数量
        concurrent_limit = 20 if fast_mode else 10
        semaphore = asyncio.Semaphore(concurrent_limit)

        async def crawl_single_with_semaphore(url):
            async with semaphore:
                if url in visited_urls:
                    return None, []

                visited_urls.add(url)

                try:
                    # 并发爬取文章和相关链接
                    article_task = self._crawl_single_article_fast(url, fetch_full_content)
                    related_task = self._extract_related_article_links_fast(url)

                    article_data, related_links = await asyncio.gather(
                        article_task,
                        related_task,
                        return_exceptions=True
                    )

                    # 处理异常结果
                    if isinstance(article_data, Exception):
                        logger.debug(f"爬取文章失败 {url}: {article_data}")
                        article_data = None

                    if isinstance(related_links, Exception):
                        logger.debug(f"提取相关链接失败 {url}: {related_links}")
                        related_links = []

                    return article_data, related_links or []

                except Exception as e:
                    logger.debug(f"批次爬取失败 {url}: {e}")
                    return None, []

        # 并发执行所有任务
        tasks = [crawl_single_with_semaphore(url) for url in urls]
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # 过滤异常结果
        valid_results = []
        for result in results:
            if isinstance(result, Exception):
                logger.debug(f"批次任务异常: {result}")
                continue
            valid_results.append(result)

        return valid_results

    async def _fetch_full_article_contents(self, articles: List[Dict[str, Any]], max_articles: int = None) -> List[Dict[str, Any]]:
        """获取文章的完整内容"""
        enhanced_articles = []
        total_articles = len(articles)

        # 如果指定了最大文章数，则限制处理数量
        if max_articles:
            articles = articles[:max_articles]
            total_articles = min(total_articles, max_articles)
            logger.info(f"限制处理文章数量为: {max_articles}")

        for i, article in enumerate(articles, 1):
            try:
                article_url = article.get('url', '')

                # 跳过无效URL
                if not article_url or article_url in ['#/', '#', '']:
                    logger.debug(f"跳过无效URL的文章: {article.get('title', '')[:50]}")
                    enhanced_articles.append(article)
                    continue

                # 确保URL是完整的
                if article_url.startswith('/'):
                    article_url = "https://xueqiu.com" + article_url
                elif not article_url.startswith('http'):
                    article_url = "https://xueqiu.com/" + article_url

                logger.info(f"正在获取第 {i}/{total_articles} 篇文章完整内容: {article.get('title', '')[:50]}...")

                # 获取完整内容
                full_content = await self._fetch_single_article_content(article_url)

                if full_content:
                    # 更新文章内容
                    article['content'] = full_content
                    article['content_fetched'] = True
                    logger.info(f"✓ 成功获取完整内容，长度: {len(full_content)} 字符")
                else:
                    article['content_fetched'] = False
                    logger.warning(f"✗ 未能获取完整内容，保留原摘要")

                enhanced_articles.append(article)

                # 添加延迟避免请求过快
                await asyncio.sleep(1)

            except Exception as e:
                logger.error(f"获取文章内容失败: {e}")
                article['content_fetched'] = False
                enhanced_articles.append(article)
                continue

        successful_count = sum(1 for a in enhanced_articles if a.get('content_fetched', False))
        logger.info(f"完整内容获取完成: {successful_count}/{total_articles} 篇成功")

        return enhanced_articles

    async def _extract_article_links_from_page(self, page_url: str) -> List[str]:
        """从页面中提取所有文章链接"""
        try:
            logger.info(f"正在从页面提取文章链接: {page_url}")

            # 使用增强的JavaScript提取页面中的所有链接（包括动态加载）
            link_extraction_js = [
                """
                (async function() {
                    console.log('🔍 开始深度提取页面中的文章链接（包括动态加载）...');

                    const links = new Set();
                    let loadMoreClicks = 0;
                    const maxLoadMore = 5;  // 最多点击5次加载更多
                    const maxScrolls = 10;  // 最多滚动10次

                    // 函数：提取当前页面的所有文章链接
                    function extractCurrentLinks() {
                        const currentLinks = new Set();
                        const allLinks = document.querySelectorAll('a[href]');

                        allLinks.forEach(link => {
                            const href = link.getAttribute('href');
                            if (href) {
                                let fullUrl = href;
                                if (href.startsWith('/')) {
                                    fullUrl = 'https://xueqiu.com' + href;
                                } else if (!href.startsWith('http')) {
                                    fullUrl = 'https://xueqiu.com/' + href;
                                }

                                // 检查是否是文章页面格式
                                if (fullUrl.match(/^https:\/\/xueqiu\.com\/\\d+\/\\d+$/)) {
                                    currentLinks.add(fullUrl);
                                }
                            }
                        });

                        return currentLinks;
                    }

                    // 函数：查找并点击加载更多按钮
                    function findAndClickLoadMore() {
                        const loadMoreSelectors = [
                            'button:contains("加载更多")',
                            'button:contains("更多")',
                            'a:contains("加载更多")',
                            'a:contains("更多")',
                            '.load-more',
                            '.more-btn',
                            '[class*="load"]',
                            '[class*="more"]'
                        ];

                        for (let selector of loadMoreSelectors) {
                            try {
                                let elements;
                                if (selector.includes(':contains')) {
                                    // 处理包含文本的选择器
                                    const text = selector.match(/contains\\("([^"]+)"\\)/)[1];
                                    const tagName = selector.split(':')[0];
                                    elements = Array.from(document.querySelectorAll(tagName)).filter(el =>
                                        el.textContent.includes(text) || el.innerText.includes(text)
                                    );
                                } else {
                                    elements = document.querySelectorAll(selector);
                                }

                                for (let element of elements) {
                                    if (element.offsetParent !== null) {  // 检查元素是否可见
                                        console.log('🔄 找到并点击加载更多按钮:', element.textContent || element.className);
                                        element.click();
                                        return true;
                                    }
                                }
                            } catch (e) {
                                console.log('查找加载更多按钮失败:', e);
                            }
                        }
                        return false;
                    }

                    // 1. 提取初始链接
                    let initialLinks = extractCurrentLinks();
                    initialLinks.forEach(link => links.add(link));
                    console.log('📖 初始提取到链接数量:', initialLinks.size);

                    // 2. 滚动页面并加载更多内容
                    for (let scroll = 0; scroll < maxScrolls; scroll++) {
                        // 滚动到页面底部
                        window.scrollTo(0, document.body.scrollHeight);
                        await new Promise(resolve => setTimeout(resolve, 1000));  // 等待内容加载

                        // 提取新链接
                        let newLinks = extractCurrentLinks();
                        let addedCount = 0;
                        newLinks.forEach(link => {
                            if (!links.has(link)) {
                                links.add(link);
                                addedCount++;
                            }
                        });

                        console.log(`📖 滚动 ${scroll + 1}: 新增 ${addedCount} 个链接，总计 ${links.size} 个`);

                        // 尝试点击加载更多按钮
                        if (loadMoreClicks < maxLoadMore) {
                            if (findAndClickLoadMore()) {
                                loadMoreClicks++;
                                console.log(`🔄 点击加载更多 ${loadMoreClicks}/${maxLoadMore}`);
                                await new Promise(resolve => setTimeout(resolve, 2000));  // 等待加载

                                // 再次提取链接
                                let moreLinks = extractCurrentLinks();
                                let moreAddedCount = 0;
                                moreLinks.forEach(link => {
                                    if (!links.has(link)) {
                                        links.add(link);
                                        moreAddedCount++;
                                    }
                                });
                                console.log(`📖 加载更多后: 新增 ${moreAddedCount} 个链接，总计 ${links.size} 个`);
                            }
                        }

                        // 如果连续几次没有新链接，提前结束
                        if (addedCount === 0 && scroll > 2) {
                            console.log('📖 连续无新链接，提前结束滚动');
                            break;
                        }
                    }

                    // 3. 从JavaScript变量中提取链接（补充）
                    try {
                        const scripts = document.querySelectorAll('script');
                        let scriptProcessed = 0;

                        for (let script of scripts) {
                            if (scriptProcessed >= 15) break;  // 增加脚本处理数量

                            const content = script.textContent || script.innerHTML;
                            if (content.length > 100000) continue;  // 跳过过大的脚本

                            const matches = content.match(/https:\/\/xueqiu\.com\/\\d+\/\\d+/g);
                            if (matches) {
                                matches.forEach(match => links.add(match));
                            }
                            scriptProcessed++;
                        }
                    } catch (e) {
                        console.log('JavaScript变量提取失败:', e);
                    }

                    const linkArray = Array.from(links);
                    console.log('📖 最终提取到文章链接数量:', linkArray.length);
                    console.log('📖 链接示例:', linkArray.slice(0, 5));

                    return linkArray;
                })();
                """
            ]

            # 爬取页面并执行JavaScript（增加超时时间以支持动态加载）
            result = await self.crawler.crawler._crawl_with_crawl4ai(
                page_url,
                js_code=link_extraction_js,
                page_timeout=60000,  # 增加到60秒以支持多次滚动和加载更多
                delay_before_return_html=2  # 稍微增加延迟确保内容加载
            )

            if result.success and result.data:
                # 从HTML中提取链接作为备用方案
                links_from_html = self._extract_links_from_html(result.data)

                # 如果JavaScript执行成功，优先使用JavaScript结果
                if hasattr(result, 'js_result') and result.js_result:
                    return result.js_result
                else:
                    return links_from_html
            else:
                logger.warning(f"页面爬取失败: {result.error}")
                return []

        except Exception as e:
            logger.error(f"提取页面链接异常: {e}")
            return []

    def _extract_links_from_html(self, html_content: str) -> List[str]:
        """从HTML中提取文章链接（备用方案）"""
        links = []
        try:
            from bs4 import BeautifulSoup
            soup = BeautifulSoup(html_content, 'html.parser')

            # 查找所有a标签
            for link in soup.find_all('a', href=True):
                href = link['href']

                # 转换为完整URL
                if href.startswith('/'):
                    full_url = 'https://xueqiu.com' + href
                elif not href.startswith('http'):
                    full_url = 'https://xueqiu.com/' + href
                else:
                    full_url = href

                # 检查是否是有效的文章页面格式
                if self.db_manager and self.db_manager._is_valid_url(full_url):
                    links.append(full_url)

            logger.info(f"从HTML中提取到 {len(links)} 个有效文章链接")

        except Exception as e:
            logger.error(f"HTML链接提取失败: {e}")

        return links

    async def _extract_article_links_with_load_more(self, page_url: str, max_load_more: int = 10) -> List[str]:
        """专门处理带有"加载更多"按钮的页面链接提取"""
        try:
            logger.info(f"正在深度提取页面链接（支持加载更多）: {page_url}")

            # 增强的动态加载JavaScript
            enhanced_js = [
                f"""
                (async function() {{
                    console.log('🚀 开始增强型链接提取（支持加载更多）...');

                    const links = new Set();
                    let totalClicks = 0;
                    const maxClicks = {max_load_more};

                    // 提取链接的函数
                    function extractLinks() {{
                        const currentLinks = new Set();
                        document.querySelectorAll('a[href]').forEach(link => {{
                            const href = link.getAttribute('href');
                            if (href && href.match(/^\/\\d+\/\\d+$|^https:\/\/xueqiu\.com\/\\d+\/\\d+$/)) {{
                                const fullUrl = href.startsWith('/') ? 'https://xueqiu.com' + href : href;
                                currentLinks.add(fullUrl);
                            }}
                        }});
                        return currentLinks;
                    }}

                    // 查找加载更多按钮的函数
                    function findLoadMoreButton() {{
                        const selectors = [
                            'button[class*="load"]',
                            'button[class*="more"]',
                            'a[class*="load"]',
                            'a[class*="more"]',
                            '.load-more',
                            '.more-btn',
                            '.load-btn'
                        ];

                        for (let selector of selectors) {{
                            const elements = document.querySelectorAll(selector);
                            for (let element of elements) {{
                                const text = element.textContent || element.innerText || '';
                                if (text.includes('更多') || text.includes('加载') || text.includes('more') || text.includes('load')) {{
                                    if (element.offsetParent !== null && !element.disabled) {{
                                        return element;
                                    }}
                                }}
                            }}
                        }}

                        // 查找包含特定文本的按钮
                        const allButtons = document.querySelectorAll('button, a, div[role="button"]');
                        for (let btn of allButtons) {{
                            const text = btn.textContent || btn.innerText || '';
                            if ((text.includes('更多') || text.includes('加载') || text.includes('more')) &&
                                btn.offsetParent !== null && !btn.disabled) {{
                                return btn;
                            }}
                        }}

                        return null;
                    }}

                    // 初始提取
                    let currentLinks = extractLinks();
                    currentLinks.forEach(link => links.add(link));
                    console.log('📖 初始链接数量:', links.size);

                    // 循环点击加载更多
                    while (totalClicks < maxClicks) {{
                        // 滚动到页面底部
                        window.scrollTo(0, document.body.scrollHeight);
                        await new Promise(resolve => setTimeout(resolve, 1500));

                        // 查找并点击加载更多按钮
                        const loadMoreBtn = findLoadMoreButton();
                        if (loadMoreBtn) {{
                            console.log('🔄 找到加载更多按钮，准备点击...', loadMoreBtn.textContent);

                            // 滚动到按钮位置
                            loadMoreBtn.scrollIntoView({{ behavior: 'smooth', block: 'center' }});
                            await new Promise(resolve => setTimeout(resolve, 1000));

                            // 点击按钮
                            try {{
                                loadMoreBtn.click();
                                totalClicks++;
                                console.log(`🔄 已点击加载更多 ${{totalClicks}}/${{maxClicks}}`);

                                // 等待内容加载
                                await new Promise(resolve => setTimeout(resolve, 3000));

                                // 提取新链接
                                let newLinks = extractLinks();
                                let addedCount = 0;
                                newLinks.forEach(link => {{
                                    if (!links.has(link)) {{
                                        links.add(link);
                                        addedCount++;
                                    }}
                                }});

                                console.log(`📖 新增链接: ${{addedCount}}, 总计: ${{links.size}}`);

                                // 如果没有新链接，可能已经到底了
                                if (addedCount === 0) {{
                                    console.log('📖 没有新链接，可能已经加载完毕');
                                    break;
                                }}

                            }} catch (e) {{
                                console.log('点击加载更多失败:', e);
                                break;
                            }}
                        }} else {{
                            console.log('📖 未找到加载更多按钮，结束加载');
                            break;
                        }}
                    }}

                    const finalLinks = Array.from(links);
                    console.log('📖 最终提取链接数量:', finalLinks.length);

                    return finalLinks;
                }})();
                """
            ]

            # 使用更长的超时时间
            result = await self.crawler.crawler._crawl_with_crawl4ai(
                page_url,
                js_code=enhanced_js,
                page_timeout=120000,  # 2分钟超时
                delay_before_return_html=3
            )

            if result.success and hasattr(result, 'js_result') and result.js_result:
                logger.info(f"增强型提取成功，获得 {len(result.js_result)} 个链接")
                return result.js_result
            else:
                logger.warning("增强型提取失败，使用备用方案")
                return self._extract_links_from_html(result.data) if result.data else []

        except Exception as e:
            logger.error(f"增强型链接提取异常: {e}")
            return []

    async def _crawl_single_article(self, article_url: str, fetch_full_content: bool = True) -> Dict[str, Any]:
        """爬取单篇文章的完整信息"""
        try:
            # 获取文章内容
            if fetch_full_content:
                content = await self._fetch_single_article_content(article_url)
            else:
                content = ""

            # 提取文章标题（从URL或内容中）
            title = self._extract_title_from_content(content) if content else self._generate_title_from_url(article_url)

            # 构建文章数据
            article_data = {
                "type": "deep_crawl_article",
                "title": title,
                "url": article_url,
                "content": content,
                "content_fetched": bool(content),
                "publish_time": "",  # 可以后续从内容中提取
                "crawl_time": time.strftime('%Y-%m-%d %H:%M:%S'),
                "source_page": "deep_crawl",
                "important": fetch_full_content and bool(content)
            }

            return article_data

        except Exception as e:
            logger.error(f"爬取单篇文章失败 {article_url}: {e}")
            return None

    async def _crawl_single_article_fast(self, article_url: str, fetch_full_content: bool = True) -> Dict[str, Any]:
        """快速爬取单篇文章（优化版本）"""
        try:
            # 获取文章内容（使用更短的超时时间）
            if fetch_full_content:
                content = await self._fetch_single_article_content_fast(article_url)
            else:
                content = ""

            # 提取文章标题
            title = self._extract_title_from_content(content) if content else self._generate_title_from_url(article_url)

            # 构建文章数据
            article_data = {
                "type": "deep_crawl_article",
                "title": title,
                "url": article_url,
                "content": content,
                "content_fetched": bool(content),
                "publish_time": "",
                "crawl_time": time.strftime('%Y-%m-%d %H:%M:%S'),
                "source_page": "deep_crawl",
                "important": fetch_full_content and bool(content)
            }

            return article_data

        except Exception as e:
            logger.debug(f"快速爬取文章失败 {article_url}: {e}")
            return None

    async def _fetch_single_article_content_fast(self, article_url: str) -> str:
        """快速获取单篇文章内容（优化版本）"""
        try:
            # 使用更简化的JavaScript和更短的超时时间
            content_extraction_js = [
                "await new Promise(resolve => setTimeout(resolve, 1000));",  # 减少等待时间
                """
                (function() {
                    // 快速提取文章内容
                    let content = '';

                    // 优先选择器（最常见的）
                    const selectors = [
                        '.article__bd__detail',
                        '.article__bd',
                        '.article-content',
                        'article',
                        '.content-main'
                    ];

                    for (let selector of selectors) {
                        const elem = document.querySelector(selector);
                        if (elem) {
                            content = elem.innerText || elem.textContent || '';
                            if (content.length > 100) break;
                        }
                    }

                    // 如果没找到，使用body内容
                    if (!content || content.length < 50) {
                        content = document.body.innerText || document.body.textContent || '';
                    }

                    return content.substring(0, 5000);  // 限制内容长度
                })();
                """
            ]

            # 使用更短的超时时间
            result = await self.crawler.crawler._crawl_with_crawl4ai(
                article_url,
                js_code=content_extraction_js,
                page_timeout=15000,  # 减少到15秒
                delay_before_return_html=1  # 减少延迟
            )

            if result.success and result.data:
                content = self._extract_article_content_from_html(result.data, article_url)
                return content[:5000] if content else ""  # 限制内容长度
            else:
                return ""

        except Exception as e:
            logger.debug(f"快速获取文章内容失败: {e}")
            return ""

    async def _extract_related_article_links(self, article_url: str) -> List[str]:
        """从文章页面中提取相关文章链接"""
        try:
            # 使用简化的方法提取相关链接
            related_links_js = [
                "await new Promise(resolve => setTimeout(resolve, 2000));",
                """
                (function() {
                    const links = new Set();

                    // 查找相关文章、推荐文章等区域的链接
                    const relatedSelectors = [
                        '.related-articles a',
                        '.recommend-articles a',
                        '.similar-articles a',
                        '.more-articles a',
                        '[class*="related"] a',
                        '[class*="recommend"] a',
                        '[class*="similar"] a'
                    ];

                    relatedSelectors.forEach(selector => {
                        const elements = document.querySelectorAll(selector);
                        elements.forEach(elem => {
                            const href = elem.getAttribute('href');
                            if (href && href.match(/^\/\\d+\/\\d+$|^https:\/\/xueqiu\.com\/\\d+\/\\d+$/)) {
                                const fullUrl = href.startsWith('/') ? 'https://xueqiu.com' + href : href;
                                links.add(fullUrl);
                            }
                        });
                    });

                    return Array.from(links);
                })();
                """
            ]

            result = await self.crawler.crawler._crawl_with_crawl4ai(
                article_url,
                js_code=related_links_js,
                page_timeout=15000,
                delay_before_return_html=2
            )

            if result.success and hasattr(result, 'js_result') and result.js_result:
                return result.js_result
            else:
                return []

        except Exception as e:
            logger.debug(f"提取相关文章链接失败 {article_url}: {e}")
            return []

    async def _extract_related_article_links_fast(self, article_url: str) -> List[str]:
        """快速提取相关文章链接（优化版本）"""
        try:
            # 使用更简化和快速的JavaScript
            related_links_js = [
                "await new Promise(resolve => setTimeout(resolve, 500));",  # 减少等待时间
                """
                (function() {
                    const links = new Set();

                    // 快速查找所有文章链接
                    const allLinks = document.querySelectorAll('a[href]');
                    let count = 0;

                    for (let link of allLinks) {
                        if (count >= 20) break;  // 限制数量加快速度

                        const href = link.getAttribute('href');
                        if (href && href.match(/^\/\\d+\/\\d+$|^https:\/\/xueqiu\.com\/\\d+\/\\d+$/)) {
                            const fullUrl = href.startsWith('/') ? 'https://xueqiu.com' + href : href;
                            links.add(fullUrl);
                            count++;
                        }
                    }

                    return Array.from(links).slice(0, 10);  // 最多返回10个链接
                })();
                """
            ]

            result = await self.crawler.crawler._crawl_with_crawl4ai(
                article_url,
                js_code=related_links_js,
                page_timeout=8000,  # 减少到8秒
                delay_before_return_html=0.5  # 减少延迟
            )

            if result.success and hasattr(result, 'js_result') and result.js_result:
                return result.js_result[:10]  # 限制返回数量
            else:
                return []

        except Exception as e:
            logger.debug(f"快速提取相关链接失败 {article_url}: {e}")
            return []

    def _extract_title_from_content(self, content: str) -> str:
        """从内容中提取标题"""
        if not content:
            return ""

        lines = content.split('\n')
        for line in lines:
            line = line.strip()
            if line and len(line) > 5 and len(line) < 200:
                return line

        # 如果没找到合适的标题，使用内容的前50个字符
        return content[:50].replace('\n', ' ').strip()

    def _generate_title_from_url(self, url: str) -> str:
        """从URL生成标题"""
        try:
            # 从URL中提取文章ID作为标题
            import re
            match = re.search(r'/(\d+)/(\d+)$', url)
            if match:
                user_id, article_id = match.groups()
                return f"雪球文章_{user_id}_{article_id}"
            else:
                return f"雪球文章_{url.split('/')[-1]}"
        except:
            return "雪球文章"

    async def _fetch_single_article_content(self, article_url: str) -> str:
        """获取单篇文章的完整内容"""
        try:
            # 使用专门的JavaScript来提取文章内容
            content_extraction_js = [
                # 等待页面加载
                "await new Promise(resolve => setTimeout(resolve, 2000));",

                # 专门针对雪球文章结构的JavaScript提取器
                """
                (function() {
                    console.log('🔍 开始提取雪球文章内容...');

                    let articleContent = '';
                    let articleTitle = '';

                    // 1. 优先提取雪球文章的标准结构
                    try {
                        // 提取文章标题
                        const titleSelectors = [
                            '.article__bd__title',
                            'h1.article__bd__title',
                            '.article-title',
                            'h1'
                        ];

                        for (let selector of titleSelectors) {
                            const titleElem = document.querySelector(selector);
                            if (titleElem) {
                                articleTitle = titleElem.innerText || titleElem.textContent || '';
                                console.log('📝 找到标题:', articleTitle.substring(0, 50));
                                break;
                            }
                        }

                        // 提取文章主体内容
                        const contentSelectors = [
                            '.article__bd__detail',  // 雪球文章详情区域
                            '.article__bd',          // 雪球文章主体
                            '.article-content',      // 通用文章内容
                            '.post-content',         // 帖子内容
                            '.content-main',         // 主要内容
                            'article',               // HTML5 article标签
                            '.detail-content'        // 详情内容
                        ];

                        for (let selector of contentSelectors) {
                            const contentElem = document.querySelector(selector);
                            if (contentElem) {
                                // 移除不需要的元素
                                const clonedElem = contentElem.cloneNode(true);

                                // 移除隐藏元素和无关内容
                                const unwantedSelectors = [
                                    '[style*="display: none"]',
                                    '[style*="display:none"]',
                                    '.ad', '.advertisement',
                                    '.nav', '.navigation',
                                    '.sidebar', '.footer',
                                    '.share', '.social',
                                    'script', 'style'
                                ];

                                unwantedSelectors.forEach(sel => {
                                    const unwanted = clonedElem.querySelectorAll(sel);
                                    unwanted.forEach(elem => elem.remove());
                                });

                                // 提取纯文本内容
                                articleContent = clonedElem.innerText || clonedElem.textContent || '';

                                // 清理多余的空白字符
                                articleContent = articleContent
                                    .replace(/\\s+/g, ' ')
                                    .replace(/\\n\\s*\\n/g, '\\n')
                                    .trim();

                                console.log('📖 找到内容区域:', selector, '长度:', articleContent.length);
                                break;
                            }
                        }

                    } catch (e) {
                        console.log('雪球结构提取失败:', e);
                    }

                    // 2. 如果没有找到专门的内容区域，使用通用方法
                    if (!articleContent || articleContent.length < 100) {
                        console.log('🔄 使用通用提取方法...');

                        // 移除导航、广告等无关内容
                        const unwantedElements = document.querySelectorAll(
                            'nav, header, footer, .nav, .menu, .ad, .advertisement, ' +
                            '.sidebar, .share, .social, script, style, .comments'
                        );
                        unwantedElements.forEach(elem => {
                            if (elem.parentNode) {
                                elem.parentNode.removeChild(elem);
                            }
                        });

                        // 获取主体内容
                        const bodyText = document.body.innerText || document.body.textContent || '';
                        if (bodyText.length > articleContent.length) {
                            articleContent = bodyText
                                .replace(/\\s+/g, ' ')
                                .replace(/\\n\\s*\\n/g, '\\n')
                                .trim();
                        }
                    }

                    // 3. 组合标题和内容
                    let finalContent = '';
                    if (articleTitle) {
                        finalContent = articleTitle + '\\n\\n' + articleContent;
                    } else {
                        finalContent = articleContent;
                    }

                    console.log('📖 最终提取内容长度:', finalContent.length);
                    console.log('📖 内容预览:', finalContent.substring(0, 200));

                    return finalContent;
                })();
                """
            ]

            # 使用爬虫获取文章内容
            result = await self.crawler.crawler._crawl_with_crawl4ai(
                article_url,
                js_code=content_extraction_js,
                page_timeout=30000,
                delay_before_return_html=3
            )

            if result.success and result.data:
                # 从HTML中提取文章内容
                content = self._extract_article_content_from_html(result.data, article_url)
                return content
            else:
                logger.warning(f"文章页面爬取失败: {result.error}")
                return None

        except Exception as e:
            logger.error(f"获取文章内容异常: {e}")
            return None

    def _extract_article_content_from_html(self, html_content: str, article_url: str) -> str:
        """从HTML中提取文章内容 - 专门针对雪球文章结构优化"""
        try:
            from bs4 import BeautifulSoup
            soup = BeautifulSoup(html_content, 'html.parser')

            article_title = ""
            article_content = ""

            # 1. 优先提取雪球文章的标准结构
            try:
                # 提取文章标题
                title_selectors = [
                    '.article__bd__title',
                    'h1.article__bd__title',
                    '.article-title',
                    'h1', 'h2'
                ]

                for selector in title_selectors:
                    title_elem = soup.select_one(selector)
                    if title_elem:
                        article_title = title_elem.get_text(strip=True)
                        logger.debug(f"找到标题: {article_title[:50]}")
                        break

                # 提取文章主体内容 - 专门针对雪球结构
                content_selectors = [
                    '.article__bd__detail',  # 雪球文章详情区域
                    '.article__bd',          # 雪球文章主体
                    '.article-content',      # 通用文章内容
                    '.post-content',         # 帖子内容
                    '.content-main',         # 主要内容
                    'article',               # HTML5 article标签
                    '.detail-content'        # 详情内容
                ]

                for selector in content_selectors:
                    content_elem = soup.select_one(selector)
                    if content_elem:
                        # 克隆元素以避免修改原始DOM
                        cloned_elem = BeautifulSoup(str(content_elem), 'html.parser')

                        # 移除不需要的元素
                        unwanted_selectors = [
                            '[style*="display: none"]',
                            '[style*="display:none"]',
                            '.ad', '.advertisement',
                            '.nav', '.navigation',
                            '.sidebar', '.footer',
                            '.share', '.social',
                            'script', 'style'
                        ]

                        for unwanted_sel in unwanted_selectors:
                            for unwanted in cloned_elem.select(unwanted_sel):
                                unwanted.decompose()

                        # 提取纯文本内容，保留段落结构
                        paragraphs = []

                        # 处理段落
                        for p in cloned_elem.select('p'):
                            p_text = p.get_text(strip=True)
                            if p_text and len(p_text) > 5:  # 过滤过短的段落
                                paragraphs.append(p_text)

                        # 如果没有找到段落，直接获取文本
                        if not paragraphs:
                            article_content = cloned_elem.get_text(strip=True)
                        else:
                            article_content = '\n\n'.join(paragraphs)

                        logger.debug(f"找到内容区域: {selector}, 长度: {len(article_content)}")
                        break

            except Exception as e:
                logger.debug(f"雪球结构提取失败: {e}")

            # 2. 如果没有找到专门的内容区域，使用通用方法
            if not article_content or len(article_content) < 100:
                logger.debug("使用通用提取方法...")

                # 移除导航、广告等无关内容
                for unwanted in soup.select('nav, header, footer, .nav, .menu, .ad, .advertisement, .sidebar, .share, .social, script, style, .comments'):
                    unwanted.decompose()

                # 获取主体内容
                body_text = soup.get_text(strip=True)
                if len(body_text) > len(article_content):
                    article_content = body_text

            # 3. 组合标题和内容
            final_content = ""
            if article_title:
                final_content = article_title + '\n\n' + article_content
            else:
                final_content = article_content

            # 4. 清理内容
            if final_content:
                import re
                # 移除多余的空白字符，但保留段落结构
                final_content = re.sub(r' +', ' ', final_content)  # 多个空格变成一个
                final_content = re.sub(r'\n +', '\n', final_content)  # 行首空格
                final_content = re.sub(r' +\n', '\n', final_content)  # 行尾空格
                final_content = re.sub(r'\n{3,}', '\n\n', final_content)  # 多个换行变成两个
                final_content = final_content.strip()

                # 限制内容长度（避免过长）
                if len(final_content) > 8000:
                    final_content = final_content[:8000] + "..."

            logger.debug(f"最终提取内容长度: {len(final_content)}")
            return final_content if final_content else None

        except Exception as e:
            logger.error(f"HTML内容提取失败: {e}")
            return None

    async def _crawl_page_articles(self, page_url: str, enhanced_mode: bool = True) -> List[Dict[str, Any]]:
        """爬取单个页面的文章"""
        try:
            if "xueqiu.com" in page_url and page_url.endswith("/today"):
                # 专门处理今日热门页面
                result = await self._crawl_today_page(page_url, enhanced_mode)
            elif "xueqiu.com" in page_url and not page_url.endswith("/today"):
                # 使用首页爬取方法
                result = await self.crawler.crawl_homepage(enhanced_mode=enhanced_mode)
            else:
                # 使用通用URL爬取方法
                result = await self.crawler.crawl_url(page_url)

            if result.success and result.data:
                return self._extract_articles_from_result(result, page_url)
            else:
                logger.error(f"页面爬取失败 {page_url}: {result.error}")
                return []

        except Exception as e:
            logger.error(f"爬取页面异常 {page_url}: {e}")
            return []

    async def _crawl_today_page(self, page_url: str, enhanced_mode: bool = True):
        """专门爬取今日热门页面"""
        try:
            # 使用增强的JavaScript来处理今日热门页面
            enhanced_js_code = [
                # 等待页面完全加载
                "await new Promise(resolve => setTimeout(resolve, 3000));",

                # 增强的滚动和内容加载策略
                """
                (async function() {
                    console.log('🔍 开始增强滚动加载今日热门内容...');

                    let initialHeight = document.body.scrollHeight;
                    let currentHeight = initialHeight;
                    let scrollAttempts = 0;
                    let maxScrollAttempts = 15; // 增加滚动次数
                    let noNewContentCount = 0;

                    // 首先尝试点击所有可能的"展开"和"加载更多"按钮
                    const expandButtons = document.querySelectorAll(
                        '.load-more, .show-more, [class*="more"], [class*="展开"], [class*="加载"], ' +
                        '[class*="unfold"], .expand, .btn-more, .more-btn, .load-btn, ' +
                        'button[class*="more"], button[class*="load"], a[class*="more"]'
                    );

                    console.log(`找到 ${expandButtons.length} 个可能的展开按钮`);

                    for (let btn of expandButtons) {
                        if (btn.offsetParent && !btn.disabled && btn.style.display !== 'none') {
                            try {
                                const btnText = btn.textContent.toLowerCase().trim();
                                if (btnText.includes('更多') || btnText.includes('展开') ||
                                    btnText.includes('加载') || btnText.includes('more') ||
                                    btnText.includes('load') || btnText.includes('expand')) {
                                    btn.click();
                                    console.log(`📖 点击了按钮: ${btn.textContent.trim()}`);
                                    await new Promise(resolve => setTimeout(resolve, 1500));
                                }
                            } catch (e) {
                                console.log('点击按钮失败:', e);
                            }
                        }
                    }

                    // 智能滚动策略 - 持续滚动直到没有新内容
                    while (scrollAttempts < maxScrollAttempts && noNewContentCount < 3) {
                        scrollAttempts++;

                        // 记录滚动前的高度
                        let beforeScrollHeight = document.body.scrollHeight;

                        // 滚动到页面底部
                        window.scrollTo({
                            top: document.body.scrollHeight,
                            behavior: 'smooth'
                        });

                        console.log(`第 ${scrollAttempts} 次滚动，页面高度: ${beforeScrollHeight}`);

                        // 等待内容加载
                        await new Promise(resolve => setTimeout(resolve, 3000));

                        // 检查是否有新内容加载
                        let afterScrollHeight = document.body.scrollHeight;

                        if (afterScrollHeight > beforeScrollHeight) {
                            console.log(`✓ 检测到新内容，高度从 ${beforeScrollHeight} 增加到 ${afterScrollHeight}`);
                            noNewContentCount = 0; // 重置计数器

                            // 再次查找并点击新出现的按钮
                            const newButtons = document.querySelectorAll(
                                '.load-more, .show-more, [class*="more"], [class*="展开"], [class*="加载"]'
                            );

                            for (let btn of newButtons) {
                                if (btn.offsetParent && !btn.disabled) {
                                    try {
                                        btn.click();
                                        console.log('📖 点击了新出现的按钮');
                                        await new Promise(resolve => setTimeout(resolve, 1000));
                                    } catch (e) {
                                        // 忽略点击错误
                                    }
                                }
                            }
                        } else {
                            noNewContentCount++;
                            console.log(`⚠️ 未检测到新内容 (${noNewContentCount}/3)`);
                        }

                        // 随机滚动到中间位置，然后再滚动到底部（模拟真实用户行为）
                        if (scrollAttempts % 3 === 0) {
                            window.scrollTo({
                                top: document.body.scrollHeight * 0.7,
                                behavior: 'smooth'
                            });
                            await new Promise(resolve => setTimeout(resolve, 1000));
                        }
                    }

                    // 最终滚动到顶部，确保所有内容都被渲染
                    window.scrollTo({ top: 0, behavior: 'smooth' });
                    await new Promise(resolve => setTimeout(resolve, 2000));

                    // 再滚动到底部
                    window.scrollTo({ top: document.body.scrollHeight, behavior: 'smooth' });
                    await new Promise(resolve => setTimeout(resolve, 2000));

                    let finalHeight = document.body.scrollHeight;
                    console.log(`🎉 滚动完成！初始高度: ${initialHeight}, 最终高度: ${finalHeight}, 增加了: ${finalHeight - initialHeight}px`);
                    console.log(`总共进行了 ${scrollAttempts} 次滚动尝试`);
                })();
                """
            ]

            # 直接使用底层的_crawl_with_crawl4ai方法来获取原始HTML
            result = await self.crawler.crawler._crawl_with_crawl4ai(
                page_url,
                js_code=enhanced_js_code,
                page_timeout=120000,  # 增加到2分钟，确保有足够时间滚动
                delay_before_return_html=8  # 增加等待时间
            )

            # 包装成CrawlResult格式，但不进行数据提取
            from crawler_engine import CrawlResult
            if result.success and result.data:
                return CrawlResult(
                    success=True,
                    data=result.data,  # 原始HTML
                    raw_html=result.data,
                    url=page_url,
                    response_time=0,
                    data_type="today_page"
                )
            else:
                return CrawlResult(
                    success=False,
                    error=result.error,
                    url=page_url,
                    response_time=0,
                    data_type="today_page"
                )

        except Exception as e:
            logger.error(f"今日热门页面爬取异常: {e}")
            from crawler_engine import CrawlResult
            return CrawlResult(success=False, error=str(e), url=page_url)

    def _extract_articles_from_result(self, result: CrawlResult, page_url: str) -> List[Dict[str, Any]]:
        """从爬取结果中提取文章"""
        articles = []

        try:
            # 检查是否是今日热门页面的原始HTML数据
            if result.data_type == "today_page" or "/today" in page_url:
                # 直接从HTML提取文章
                if hasattr(result, 'raw_html') and result.raw_html:
                    articles = self._extract_articles_from_html(result.raw_html, page_url)
                elif isinstance(result.data, str):
                    # 如果data就是HTML字符串
                    articles = self._extract_articles_from_html(result.data, page_url)
                return articles

            # 处理结构化数据（首页等）
            if hasattr(result.data, 'hot_topics') and result.data.hot_topics:
                # 提取热门话题
                for topic in result.data.hot_topics:
                    articles.append({
                        "type": "hot_topic",
                        "title": topic.title,
                        "author": topic.author,
                        "url": topic.url,
                        "view_count": topic.view_count,
                        "comment_count": topic.comment_count,
                        "source_page": page_url,
                        "crawl_time": result.data.crawl_time if hasattr(result.data, 'crawl_time') else None
                    })

            if hasattr(result.data, 'user_posts') and result.data.user_posts:
                # 提取用户文章/动态
                for post in result.data.user_posts:
                    articles.append({
                        "type": "user_post",
                        "title": post.content[:100] + "..." if len(post.content) > 100 else post.content,
                        "content": post.content,
                        "author": post.author,
                        "url": post.post_url,
                        "like_count": post.like_count,
                        "comment_count": post.comment_count,
                        "publish_time": post.publish_time,
                        "mentioned_stocks": post.mentioned_stocks,
                        "source_page": page_url,
                        "crawl_time": result.data.crawl_time if hasattr(result.data, 'crawl_time') else None
                    })

            # 如果没有结构化数据，尝试从原始HTML解析
            if not articles and hasattr(result, 'raw_html') and result.raw_html:
                articles = self._extract_articles_from_html(result.raw_html, page_url)

        except Exception as e:
            logger.error(f"提取文章失败: {e}")

        return articles

    def _extract_articles_from_html(self, html_content: str, page_url: str) -> List[Dict[str, Any]]:
        """从HTML中直接提取文章"""
        articles = []

        try:
            from bs4 import BeautifulSoup
            soup = BeautifulSoup(html_content, 'html.parser')

            # 针对今日热门页面的特殊处理
            if "/today" in page_url:
                articles.extend(self._extract_today_articles(soup, page_url))

            # 通用文章元素选择器
            article_selectors = [
                '[class*="timeline"]', '[class*="feed"]', '[class*="post"]',
                '.status-item', '.timeline-item', '[class*="status"]',
                '[class*="topic"]', '[class*="article"]', '[class*="card"]',
                '[class*="item"]', '[class*="content"]'
            ]

            for selector in article_selectors:
                elements = soup.select(selector)
                for element in elements:
                    article = self._extract_article_from_element(element, page_url)
                    if article and not self._is_duplicate_article(article, articles):
                        articles.append(article)

        except Exception as e:
            logger.error(f"HTML解析失败: {e}")

        return articles

    def _extract_today_articles(self, soup, page_url: str) -> List[Dict[str, Any]]:
        """专门提取今日热门页面的文章"""
        articles = []

        try:
            # 今日热门页面的特殊选择器 - 更全面的内容捕获
            today_selectors = [
                # 主要内容容器
                '[class*="timeline"]', '[class*="feed"]', '[class*="status"]',
                # 文章和帖子
                '[class*="card"]', '[class*="item"]', '[class*="post"]', '[class*="article"]',
                # 列表项和容器
                'li[class*="item"]', 'div[class*="item"]', 'div[class*="status"]',
                # 内容区域
                '[class*="content"]', '[class*="body"]', '[class*="text"]', '[class*="desc"]',
                # 用户动态和评论
                '[class*="user"]', '[class*="comment"]', '[class*="reply"]',
                # 任何包含文本的div（作为兜底）
                'div[class]:not([class*="nav"]):not([class*="header"]):not([class*="footer"]):not([class*="menu"])',
                # 段落和文本元素
                'p', 'span[class*="text"]', 'span[class*="content"]',
                # 链接元素
                'a[href*="/"]'
            ]

            processed_urls = set()  # 避免重复URL

            for selector in today_selectors:
                elements = soup.select(selector)
                logger.info(f"今日热门选择器 {selector} 找到 {len(elements)} 个元素")

                for element in elements:
                    try:
                        # 提取文章信息
                        article_data = self._extract_today_article_data(element, page_url)

                        if article_data:
                            # 更宽松的过滤条件
                            title = article_data.get('title', '')
                            url = article_data.get('url', '')
                            content = article_data.get('content', '')

                            # 只要有标题或内容，且长度合理就保留
                            if (len(title) >= 5 or len(content) >= 10):
                                # 使用URL或内容的hash作为去重标识
                                unique_key = url if url else hash(content[:100])

                                if unique_key not in processed_urls:
                                    processed_urls.add(unique_key)
                                    articles.append(article_data)

                    except Exception as e:
                        logger.debug(f"提取今日文章元素失败: {e}")
                        continue

            logger.info(f"今日热门页面提取到 {len(articles)} 篇文章")

        except Exception as e:
            logger.error(f"今日热门文章提取失败: {e}")

        return articles

    def _extract_today_article_data(self, element, page_url: str) -> Dict[str, Any]:
        """从今日热门页面元素提取文章数据 - 增强版，采集所有内容"""
        try:
            # 提取标题 - 更宽松的策略
            title_selectors = [
                'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
                '.title', '[class*="title"]', '[class*="subject"]',
                '.content', '[class*="content"]', '[class*="text"]',
                'a[href*="/"]', 'span[class*="text"]', 'div[class*="text"]',
                'p', 'strong', 'b'
            ]

            title = ""
            title_element = None
            all_text_content = ""

            # 首先获取元素的所有文本内容
            all_text_content = element.get_text(strip=True)

            # 尝试找到最佳标题
            for sel in title_selectors:
                title_elem = element.select_one(sel)
                if title_elem:
                    title_text = title_elem.get_text(strip=True)
                    # 选择最长且合理的文本作为标题
                    if len(title_text) > len(title) and len(title_text) <= 200:
                        title = title_text
                        title_element = title_elem

            # 如果没有找到合适的标题，使用内容的前100个字符作为标题
            if not title and all_text_content:
                title = all_text_content[:100] + "..." if len(all_text_content) > 100 else all_text_content

            # 过滤无效内容
            if not title or len(title.strip()) < 5:
                return None

            # 过滤纯时间戳内容
            import re
            time_pattern = r'^[^昨天今天前天\d]+[昨天今天前天]\s*\d{2}:\d{2}$|^\d{2}-\d{2}\s*\d{2}:\d{2}$|^\d+小时前$|^\d+分钟前$'
            if re.match(time_pattern, title.strip()):
                return None

            # 过滤过短的纯用户名内容
            if len(title.strip()) < 15 and not any(char in title for char in ['$', '股', '市', '投资', '分析']):
                return None

            # 提取URL
            url = ""
            if title_element and title_element.name == 'a':
                url = title_element.get('href', '')
            else:
                link_elem = element.select_one('a[href]')
                if link_elem:
                    url = link_elem.get('href', '')

            # 处理相对URL
            if url and url.startswith('/'):
                url = "https://xueqiu.com" + url

            # 提取作者
            author_selectors = [
                '.author', '.username', '.user-name', '[class*="user"]',
                '[class*="author"]', 'span[class*="name"]'
            ]

            author = ""
            for sel in author_selectors:
                author_elem = element.select_one(sel)
                if author_elem:
                    author = author_elem.get_text(strip=True)
                    if author:
                        break

            # 提取时间
            time_selectors = [
                'time', '.time', '[class*="time"]', '.date', '[class*="date"]'
            ]

            publish_time = ""
            for sel in time_selectors:
                time_elem = element.select_one(sel)
                if time_elem:
                    publish_time = time_elem.get_text(strip=True)
                    break

            # 提取内容摘要 - 更全面的内容获取
            content_selectors = [
                '.desc', '.description', '.summary', '[class*="desc"]',
                '.content', '[class*="content"]', '[class*="text"]',
                'p', 'div[class*="body"]', 'span[class*="text"]',
                '.message', '[class*="message"]', '.post-content'
            ]

            content = ""

            # 首先尝试从特定选择器获取内容
            for sel in content_selectors:
                content_elem = element.select_one(sel)
                if content_elem and content_elem != title_element:
                    content_text = content_elem.get_text(strip=True)
                    if len(content_text) > len(content) and content_text != title:
                        content = content_text

            # 如果没有找到专门的内容区域，使用所有文本内容
            if not content and all_text_content:
                content = all_text_content
                # 如果内容和标题相同，尝试获取更多内容
                if content == title:
                    # 查找所有文本节点
                    all_texts = []
                    for text_elem in element.find_all(string=True):
                        text = text_elem.strip()
                        if text and len(text) > 10:
                            all_texts.append(text)

                    if all_texts:
                        content = " ".join(all_texts)

            # 确保内容不为空
            if not content:
                content = title  # 至少使用标题作为内容

            # 提取统计数据
            stats = self._extract_article_stats(element)

            return {
                "type": "today_article",
                "title": title,
                "content": content[:500] + "..." if len(content) > 500 else content,
                "author": author,
                "url": url,
                "publish_time": publish_time,
                "view_count": stats.get('view_count'),
                "like_count": stats.get('like_count'),
                "comment_count": stats.get('comment_count'),
                "source_page": page_url,
                "crawl_time": None
            }

        except Exception as e:
            logger.debug(f"今日文章数据提取失败: {e}")
            return None

    def _extract_article_stats(self, element) -> Dict[str, Any]:
        """提取文章统计数据"""
        stats = {}

        try:
            # 查找数字统计
            stat_selectors = [
                '[class*="count"]', '[class*="num"]', '[class*="stat"]',
                '.like', '.view', '.comment', '.share'
            ]

            for selector in stat_selectors:
                stat_elements = element.select(selector)
                for stat_elem in stat_elements:
                    text = stat_elem.get_text(strip=True)

                    # 提取数字
                    import re
                    numbers = re.findall(r'\d+', text)
                    if numbers:
                        number = int(numbers[0])

                        # 根据上下文判断统计类型
                        text_lower = text.lower()
                        if any(keyword in text_lower for keyword in ['like', '赞', '点赞']):
                            stats['like_count'] = number
                        elif any(keyword in text_lower for keyword in ['view', '浏览', '阅读']):
                            stats['view_count'] = number
                        elif any(keyword in text_lower for keyword in ['comment', '评论', '回复']):
                            stats['comment_count'] = number

        except Exception as e:
            logger.debug(f"统计数据提取失败: {e}")

        return stats

    def _extract_article_from_element(self, element, page_url: str) -> Dict[str, Any]:
        """从DOM元素提取文章信息"""
        try:
            # 提取标题/内容
            title_selectors = ['.title', '.content', '.text', 'h1', 'h2', 'h3', 'p']
            title = ""
            for sel in title_selectors:
                title_elem = element.select_one(sel)
                if title_elem:
                    title = title_elem.get_text(strip=True)
                    break

            if not title or len(title) < 10:
                return None

            # 提取作者
            author_selectors = ['.author', '.username', '.user-name', '[class*="user"]']
            author = ""
            for sel in author_selectors:
                author_elem = element.select_one(sel)
                if author_elem:
                    author = author_elem.get_text(strip=True)
                    break

            # 提取链接
            url = ""
            link_elem = element.select_one('a')
            if link_elem and link_elem.get('href'):
                url = link_elem.get('href')
                if url.startswith('/'):
                    url = "https://xueqiu.com" + url

            return {
                "type": "article",
                "title": title,
                "author": author,
                "url": url,
                "source_page": page_url,
                "crawl_time": None
            }

        except Exception as e:
            logger.debug(f"元素解析失败: {e}")
            return None

    def _is_duplicate_article(self, new_article: Dict[str, Any], existing_articles: List[Dict[str, Any]]) -> bool:
        """检查文章是否重复"""
        new_title = new_article.get('title', '').strip()

        for existing in existing_articles:
            existing_title = existing.get('title', '').strip()
            if new_title == existing_title:
                return True

        return False

    def get_database_stats(self) -> Dict[str, Any]:
        """获取数据库统计信息"""
        if not self.save_to_database or not self.db_manager:
            return {'database_enabled': False}

        try:
            total_count = self.db_manager.get_article_count()
            recent_articles = self.db_manager.get_recent_articles(5)

            return {
                'database_enabled': True,
                'total_articles': total_count,
                'recent_articles': recent_articles
            }
        except Exception as e:
            logger.error(f"获取数据库统计失败: {e}")
            return {'database_enabled': True, 'error': str(e)}

    def save_articles_to_database(self, articles: List[Dict[str, Any]]) -> Dict[str, int]:
        """手动保存文章到数据库"""
        if not self.save_to_database or not self.db_manager:
            logger.warning("数据库功能未启用")
            return {'success': 0, 'failed': 0, 'skipped': 0}

        return self.db_manager.save_articles(articles)

    def _deduplicate_articles(self, articles: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """去重文章"""
        unique_articles = []
        seen_titles = set()

        for article in articles:
            title = article.get('title', '').strip()
            if title and title not in seen_titles and len(title) >= 10:
                seen_titles.add(title)
                unique_articles.append(article)

        return unique_articles

    def save_results(self, results: List[Dict[str, Any]], filename: str = None):
        """保存结果到文件"""
        if not filename:
            filename = f"popular_articles_{int(asyncio.get_event_loop().time())}.json"

        output_path = Path(filename)

        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)

        logger.info(f"结果已保存到: {output_path}")

        # 尝试保存为CSV格式（如果pandas可用）
        try:
            self._save_as_csv(results, output_path.with_suffix('.csv'))
        except Exception as e:
            logger.warning(f"CSV保存失败（pandas兼容性问题），已跳过: {str(e)[:100]}...")
            logger.info("JSON文件保存成功，可以正常使用")

    def _save_as_csv(self, results: List[Dict[str, Any]], csv_path: Path):
        """保存为CSV格式"""
        try:
            import pandas as pd

            if results:
                df = pd.DataFrame(results)
                df.to_csv(csv_path, index=False, encoding='utf-8')
                logger.info(f"CSV结果已保存到: {csv_path}")
        except ImportError:
            logger.warning("pandas未安装，跳过CSV保存")















async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='热门文章爬虫 - 专门爬取各个页面的热门文章')
    parser.add_argument('--pages', nargs='+',
                       help='要爬取的页面URL列表')
    parser.add_argument('--output', type=str,
                       help='输出文件名')
    parser.add_argument('--disable-anti-detection', action='store_true',
                       help='禁用反检测功能')
    parser.add_argument('--enhanced', action='store_true', default=True,
                       help='启用增强模式（自动展开和滚动加载）')
    parser.add_argument('--full-content', action='store_true', default=True,
                       help='获取文章完整内容（默认启用）')
    parser.add_argument('--no-full-content', action='store_true',
                       help='仅获取文章摘要，不获取完整内容')
    parser.add_argument('--save-to-database', action='store_true', default=True,
                       help='保存到数据库（默认启用）')
    parser.add_argument('--no-database', action='store_true',
                       help='不保存到数据库，仅保存JSON文件')
    parser.add_argument('--database-url', type=str,
                       help='自定义数据库连接URL')
    parser.add_argument('--db-stats', action='store_true',
                       help='显示数据库统计信息')
    parser.add_argument('--deep-crawl', action='store_true',
                       help='启用深度爬取模式，从今日热门页面开始深度爬取所有文章链接')
    parser.add_argument('--max-depth', type=int, default=3,
                       help='深度爬取的最大深度（默认3层）')
    parser.add_argument('--max-articles', type=int, default=1000,
                       help='深度爬取的最大文章数量（默认1000篇）')
    parser.add_argument('--fast-mode', action='store_true',
                       help='启用快速模式，减少等待时间和并发限制')
    parser.add_argument('--load-more-clicks', type=int, default=8,
                       help='加载更多按钮的最大点击次数（默认8次）')

    args = parser.parse_args()

    # 确定数据库保存选项
    save_to_database = args.save_to_database and not args.no_database

    # 创建爬虫应用
    app = PopularArticlesCrawler(
        enable_anti_detection=not args.disable_anti_detection,
        save_to_database=save_to_database,
        database_url=args.database_url
    )

    # 如果只是查看数据库统计
    if args.db_stats:
        stats = app.get_database_stats()
        print("\n=== 数据库统计信息 ===")
        if stats.get('database_enabled'):
            if 'error' in stats:
                print(f"❌ 数据库错误: {stats['error']}")
            else:
                print(f"📊 文章总数: {stats.get('total_articles', 0)}")
                recent = stats.get('recent_articles', [])
                if recent:
                    print(f"\n📰 最近 {len(recent)} 篇文章:")
                    for i, article in enumerate(recent, 1):
                        print(f"  {i}. {article.get('title', 'N/A')[:50]}...")
                        print(f"     作者: {article.get('author', 'N/A')} | 时间: {article.get('crawl_time', 'N/A')}")
        else:
            print("❌ 数据库功能未启用")
        return
    
    try:
        # 确定是否获取完整内容
        fetch_full_content = args.full_content and not args.no_full_content

        # 根据模式选择爬取方法
        if args.deep_crawl:
            logger.info("开始深度爬取模式")
            logger.info(f"深度爬取参数: 最大深度={args.max_depth}, 最大文章数={args.max_articles}")

            # 深度爬取
            articles = await app.deep_crawl_from_today(
                max_depth=args.max_depth,
                max_articles=args.max_articles,
                fetch_full_content=fetch_full_content,
                fast_mode=args.fast_mode,
                load_more_clicks=args.load_more_clicks
            )

            logger.info(f"深度爬取完成: 共获取 {len(articles)} 篇文章")
        else:
            logger.info("开始常规爬取模式")

            # 常规爬取热门文章
            articles = await app.crawl_popular_articles(
                pages=args.pages,
                enhanced_mode=args.enhanced,
                fetch_full_content=fetch_full_content
            )

            logger.info(f"常规爬取完成: 共获取 {len(articles)} 篇热门文章")

        # 统计完整内容获取情况
        if fetch_full_content:
            full_content_count = sum(1 for a in articles if a.get('content_fetched', False))
            logger.info(f"完整内容获取: {full_content_count}/{len(articles)} 篇成功")

        # 显示数据库保存状态
        if save_to_database:
            db_stats = app.get_database_stats()
            if db_stats.get('database_enabled'):
                logger.info(f"数据库中共有 {db_stats.get('total_articles', 0)} 篇文章")

        # 保存结果
        if args.output:
            app.save_results(articles, args.output)

        # 打印部分结果预览
        print(f"\n=== 热门文章预览 (共 {len(articles)} 篇) ===")
        if fetch_full_content:
            full_content_count = sum(1 for a in articles if a.get('content_fetched', False))
            print(f"完整内容获取: {full_content_count}/{len(articles)} 篇")
            print()

        for i, article in enumerate(articles[:10], 1):  # 只显示前10篇
            print(f"{i}. 【{article.get('type', 'article')}】{article.get('title', 'N/A')}")
            if article.get('author'):
                print(f"   作者: {article.get('author')}")

            # 显示内容获取状态
            if fetch_full_content:
                if article.get('content_fetched'):
                    content_length = len(article.get('content', ''))
                    print(f"   ✓ 完整内容 ({content_length} 字符)")
                else:
                    print(f"   ⚠️ 仅摘要内容")

            if article.get('view_count') or article.get('like_count'):
                stats = []
                if article.get('view_count'):
                    stats.append(f"浏览: {article.get('view_count')}")
                if article.get('like_count'):
                    stats.append(f"点赞: {article.get('like_count')}")
                if article.get('comment_count'):
                    stats.append(f"评论: {article.get('comment_count')}")
                if stats:
                    print(f"   统计: {' | '.join(stats)}")
            print(f"   来源: {article.get('source_page', 'N/A')}")
            print()

        if len(articles) > 10:
            print(f"... 还有 {len(articles) - 10} 篇文章，详见输出文件")

    except KeyboardInterrupt:
        logger.info("用户中断爬取")
    except Exception as e:
        logger.error(f"爬取过程中发生错误: {e}")
        raise

async def demo_crawl():
    """演示热门文章爬取"""
    print("=== 热门文章爬虫演示 ===")

    app = PopularArticlesCrawler(enable_anti_detection=True)

    # 演示热门文章爬取
    print("\n开始爬取热门文章...")
    print("-" * 40)

    # 先获取文章列表，然后获取完整内容
    articles = await app.crawl_popular_articles(enhanced_mode=True, fetch_full_content=True)

    if articles:
        print(f"✓ 热门文章爬取成功，共获取 {len(articles)} 篇文章")

        # 统计完整内容获取情况
        full_content_count = sum(1 for a in articles if a.get('content_fetched', False))
        print(f"✓ 完整内容获取: {full_content_count}/{len(articles)} 篇")

        # 显示前3篇文章（包含完整内容）
        print("\n前3篇热门文章（含完整内容）:")
        for i, article in enumerate(articles[:3], 1):
            print(f"{i}. 【{article.get('type', 'article')}】{article.get('title', 'N/A')}")
            if article.get('author'):
                print(f"   作者: {article.get('author')}")

            # 显示内容预览
            content = article.get('content', '')
            if content:
                preview = content[:200] + "..." if len(content) > 200 else content
                print(f"   内容: {preview}")

            if article.get('content_fetched'):
                print(f"   ✓ 已获取完整内容 ({len(content)} 字符)")
            else:
                print(f"   ⚠️ 仅摘要内容")

            print(f"   来源: {article.get('source_page', 'N/A')}")
            print()
    else:
        print("✗ 未获取到热门文章")

    print("\n=== 使用说明 ===")
    print("命令行使用示例:")
    print("  python main_advanced.py                                    # 使用默认页面爬取（含完整内容+数据库）")
    print("  python main_advanced.py --pages https://xueqiu.com         # 指定页面爬取")
    print("  python main_advanced.py --output articles.json            # 指定输出文件")
    print("  python main_advanced.py --no-full-content                 # 仅获取摘要，不获取完整内容")
    print("  python main_advanced.py --no-database                     # 不保存到数据库")
    print("  python main_advanced.py --db-stats                        # 查看数据库统计")
    print("  python main_advanced.py --database-url mysql://...        # 自定义数据库连接")
    print("  python main_advanced.py --disable-anti-detection          # 禁用反检测")
    print("\n深度爬取模式:")
    print("  python main_advanced.py --deep-crawl                      # 🔥 启用深度爬取模式")
    print("  python main_advanced.py --deep-crawl --max-depth 5        # 设置最大深度为5层")
    print("  python main_advanced.py --deep-crawl --max-articles 2000  # 设置最大文章数为2000篇")
    print("  python main_advanced.py --deep-crawl --no-full-content    # 深度爬取但不获取完整内容")
    print("  python main_advanced.py --deep-crawl --fast-mode          # ⚡ 启用快速模式（更高并发）")
    print("  python main_advanced.py --deep-crawl --load-more-clicks 15 # 🔄 设置加载更多点击次数")
    print("\n深度爬取说明:")
    print("  - 从 https://xueqiu.com/today 页面开始")
    print("  - 提取所有文章页面链接（格式: https://xueqiu.com/数字/数字）")
    print("  - 逐层深度爬取，从每个文章页面中提取更多相关文章链接")
    print("  - 自动过滤无效链接，只保留真正的文章页面")
    print("  - 支持设置最大深度和最大文章数量限制")
    print("  - 快速模式: 提高并发数量，减少等待时间，加快爬取速度")
    print("  - 动态加载: 自动滚动页面并点击'加载更多'按钮获取更多链接")
    print("\n数据库配置:")
    print("  环境变量:")
    print("    MYSQL_HOST=localhost")
    print("    MYSQL_PORT=3306")
    print("    MYSQL_USER=root")
    print("    MYSQL_PASSWORD=your_password")
    print("    MYSQL_DATABASE=xueqiu_crawler")

if __name__ == "__main__":
    # 如果没有命令行参数，运行演示
    import sys
    if len(sys.argv) == 1:
        asyncio.run(demo_crawl())
    else:
        asyncio.run(main())

# aicrawl数据模型迁移总结

## 迁移概述

已成功将aicrawl模块的数据存储从独立的XueqiuArticle模型迁移到使用crawl模块中的Media模型，实现数据统一存储。

## 主要变更

### 1. 数据库模型统一

**之前**: 独立的XueqiuArticle模型
```python
class XueqiuArticle(Base):
    __tablename__ = 'xueqiu_articles'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    article_id = Column(String(64), unique=True, nullable=False)
    source = Column(String(16), default='xueqiu')
    title = Column(String(255), nullable=False)
    # ... 更多字段
```

**现在**: 使用crawl模块的Media模型
```python
class Media(Base):
    __tablename__ = 'media'
    
    id = Column(Integer, primary_key=True)
    mediaId = Column(String(40))
    source = Column(String(8))
    title = Column(String(120))
    url = Column(String(200))
    createTime = Column(String(10))
    content = Column(Text)
    important = Column(Boolean)
```

### 2. 字段映射调整

| 原XueqiuArticle字段 | 新Media字段 | 数据类型变化 | 说明 |
|---------------------|-------------|--------------|------|
| article_id | mediaId | String(64) → String(40) | 文章唯一标识 |
| source | source | String(16) → String(8) | 数据源标识 |
| title | title | String(255) → String(120) | 文章标题 |
| url | url | String(512) → String(200) | 文章链接 |
| content | content | Text → Text | 文章内容 |
| publish_time | createTime | String(32) → String(10) | 时间格式：YYYY-MM-DD |
| content_fetched | important | Boolean → Boolean | 重要性标记 |
| author | - | 删除 | Media模型无此字段 |
| crawl_time | - | 删除 | 不再单独存储 |
| created_at | - | 删除 | 使用数据库默认时间戳 |
| updated_at | - | 删除 | 使用数据库默认时间戳 |

### 3. 数据库配置统一

**之前**: 独立的db_config.py文件
- 支持多种环境变量
- 独立的配置管理

**现在**: 直接使用crawl模块配置
- 统一使用BB_MYSQL_*环境变量
- 移除db_config.py文件
- 使用mysql-connector-python连接器

### 4. 文件变更

#### 删除的文件
- `core/aicrawl/db_config.py` - 独立数据库配置文件

#### 修改的文件
- `core/aicrawl/database.py` - 主要修改
- `core/aicrawl/main_advanced.py` - 移除db_config导入
- `core/aicrawl/init_database.py` - 移除db_config依赖
- `core/aicrawl/requirements_database.txt` - 更新依赖

#### 新增的文件
- `core/aicrawl/test_media_integration.py` - 完整集成测试
- `core/aicrawl/quick_test.py` - 快速功能测试
- `core/aicrawl/simple_test.py` - 简化测试
- `core/aicrawl/MIGRATION_SUMMARY.md` - 本文档

## 环境变量配置

现在统一使用以下环境变量：

```bash
# 数据库连接配置
export BB_MYSQL_HOST=localhost
export BB_MYSQL_PORT=3306
export BB_MYSQL_USER=root
export BB_MYSQL_PASSWD=your_password
export BB_MYSQL_DBNAME=blackbear
```

## 数据转换逻辑

新的数据转换逻辑会自动处理字段长度限制：

```python
def _convert_to_db_model(self, article):
    return {
        'mediaId': self._generate_media_id(article),  # 40字符限制
        'source': 'xueqiu',                          # 8字符限制
        'title': article.get('title', '')[:120],     # 120字符限制
        'url': article.get('url', '')[:200],         # 200字符限制
        'createTime': create_time[:10],              # 10字符限制 (YYYY-MM-DD)
        'content': article.get('content', ''),       # 无限制
        'important': article.get('important', False) # 布尔值
    }
```

## 测试验证

提供了多个测试文件来验证迁移结果：

1. **quick_test.py** - 快速基本功能测试
2. **simple_test.py** - 简化的模型测试
3. **test_media_integration.py** - 完整的集成测试

运行测试：
```bash
cd core/aicrawl
python quick_test.py
```

## 兼容性说明

### API兼容性
- 保持了原有的DatabaseManager接口
- save_articles()方法签名不变
- get_article_count()和get_recent_articles()方法保持兼容

### 数据兼容性
- 新数据将存储到media表
- 原xueqiu_articles表数据需要手动迁移（如果需要）
- 字段长度限制可能导致数据截断

## 优势

1. **数据统一**: aicrawl和crawl模块共享同一个media表
2. **配置简化**: 移除重复的数据库配置
3. **维护性**: 减少代码重复，统一数据模型
4. **扩展性**: 便于后续添加其他数据源

## 注意事项

1. **字段长度**: 新模型的字段长度限制更严格，可能导致数据截断
2. **时间格式**: createTime字段只存储日期部分(YYYY-MM-DD)
3. **缺失字段**: author等字段不再存储
4. **依赖**: 需要安装mysql-connector-python

## 后续工作

1. 如需保留历史数据，可编写数据迁移脚本
2. 考虑在Media模型中添加author字段（需要修改crawl模块）
3. 监控新模型的性能表现
4. 根据需要调整字段长度限制

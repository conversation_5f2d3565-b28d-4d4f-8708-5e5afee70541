#!/usr/bin/env python3
"""
测试动态加载功能
验证"加载更多"按钮的处理和链接提取效果
"""

import asyncio
import sys
import os
import time
import logging

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_basic_link_extraction():
    """测试基础链接提取"""
    print("=" * 60)
    print("测试基础链接提取")
    print("=" * 60)
    
    try:
        from main_advanced import PopularArticlesCrawler
        
        crawler = PopularArticlesCrawler(save_to_database=False)
        
        print("正在进行基础链接提取...")
        start_time = time.time()
        
        basic_links = await crawler._extract_article_links_from_page("https://xueqiu.com/today")
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"基础提取完成:")
        print(f"  提取链接数量: {len(basic_links)}")
        print(f"  耗时: {duration:.2f} 秒")
        
        if basic_links:
            print(f"  前5个链接示例:")
            for i, link in enumerate(basic_links[:5], 1):
                print(f"    {i}. {link}")
        
        return len(basic_links), duration
        
    except Exception as e:
        print(f"❌ 基础链接提取测试失败: {e}")
        return 0, 0

async def test_enhanced_link_extraction(load_more_clicks=5):
    """测试增强型链接提取（支持加载更多）"""
    print(f"\n{'=' * 60}")
    print(f"测试增强型链接提取（加载更多 {load_more_clicks} 次）")
    print("=" * 60)
    
    try:
        from main_advanced import PopularArticlesCrawler
        
        crawler = PopularArticlesCrawler(save_to_database=False)
        
        print(f"正在进行增强型链接提取（最多点击 {load_more_clicks} 次加载更多）...")
        start_time = time.time()
        
        enhanced_links = await crawler._extract_article_links_with_load_more(
            "https://xueqiu.com/today", 
            max_load_more=load_more_clicks
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"增强型提取完成:")
        print(f"  提取链接数量: {len(enhanced_links)}")
        print(f"  耗时: {duration:.2f} 秒")
        print(f"  平均每次加载更多获得: {len(enhanced_links)/max(1, load_more_clicks):.1f} 个链接")
        
        if enhanced_links:
            print(f"  前5个链接示例:")
            for i, link in enumerate(enhanced_links[:5], 1):
                print(f"    {i}. {link}")
        
        return len(enhanced_links), duration
        
    except Exception as e:
        print(f"❌ 增强型链接提取测试失败: {e}")
        return 0, 0

async def test_link_extraction_comparison():
    """对比基础提取和增强型提取的效果"""
    print(f"\n{'=' * 60}")
    print("对比基础提取 vs 增强型提取")
    print("=" * 60)
    
    # 测试基础提取
    basic_count, basic_duration = await test_basic_link_extraction()
    
    # 测试增强型提取
    enhanced_count, enhanced_duration = await test_enhanced_link_extraction(load_more_clicks=8)
    
    # 输出对比结果
    print(f"\n📊 提取效果对比:")
    print(f"  基础提取: {basic_count} 个链接, {basic_duration:.2f} 秒")
    print(f"  增强提取: {enhanced_count} 个链接, {enhanced_duration:.2f} 秒")
    
    if basic_count > 0 and enhanced_count > 0:
        improvement = enhanced_count / basic_count
        print(f"  链接数量提升: {improvement:.2f}x")
        
        if improvement > 2.0:
            print("  ✅ 增强型提取显著提升了链接数量！")
        elif improvement > 1.5:
            print("  ✅ 增强型提取有效提升了链接数量")
        else:
            print("  ⚠️  增强型提取效果有限")
    
    return basic_count, enhanced_count

async def test_different_load_more_counts():
    """测试不同加载更多次数的效果"""
    print(f"\n{'=' * 60}")
    print("测试不同加载更多次数的效果")
    print("=" * 60)
    
    test_counts = [3, 5, 8, 12]
    results = []
    
    for count in test_counts:
        print(f"\n🔄 测试加载更多 {count} 次...")
        
        try:
            from main_advanced import PopularArticlesCrawler
            crawler = PopularArticlesCrawler(save_to_database=False)
            
            start_time = time.time()
            links = await crawler._extract_article_links_with_load_more(
                "https://xueqiu.com/today", 
                max_load_more=count
            )
            end_time = time.time()
            
            duration = end_time - start_time
            results.append((count, len(links), duration))
            
            print(f"  结果: {len(links)} 个链接, {duration:.2f} 秒")
            
        except Exception as e:
            print(f"  ❌ 测试失败: {e}")
            results.append((count, 0, 0))
    
    # 输出汇总结果
    print(f"\n📊 不同加载更多次数效果对比:")
    print(f"{'次数':<6} {'链接数':<8} {'耗时(秒)':<10} {'效率(链接/秒)':<12}")
    print("-" * 40)
    
    for count, links, duration in results:
        efficiency = links / duration if duration > 0 else 0
        print(f"{count:<6} {links:<8} {duration:<10.2f} {efficiency:<12.2f}")
    
    # 给出建议
    if results:
        best_efficiency = max(results, key=lambda x: x[1]/x[2] if x[2] > 0 else 0)
        best_count = max(results, key=lambda x: x[1])
        
        print(f"\n💡 建议:")
        print(f"  最高效率: 加载更多 {best_efficiency[0]} 次 ({best_efficiency[1]/best_efficiency[2]:.2f} 链接/秒)")
        print(f"  最多链接: 加载更多 {best_count[0]} 次 ({best_count[1]} 个链接)")

async def test_mini_deep_crawl_with_dynamic():
    """测试带动态加载的小规模深度爬取"""
    print(f"\n{'=' * 60}")
    print("测试带动态加载的小规模深度爬取")
    print("=" * 60)
    
    try:
        from main_advanced import PopularArticlesCrawler
        
        crawler = PopularArticlesCrawler(save_to_database=False)
        
        print("开始小规模深度爬取（支持动态加载）...")
        print("参数: 最大深度=1, 最大文章数=20, 加载更多=5次")
        
        start_time = time.time()
        articles = await crawler.deep_crawl_from_today(
            max_depth=1,
            max_articles=20,
            fetch_full_content=False,  # 不获取完整内容以加快测试
            fast_mode=True,
            load_more_clicks=5
        )
        end_time = time.time()
        
        duration = end_time - start_time
        
        print(f"深度爬取完成:")
        print(f"  获取文章数量: {len(articles)}")
        print(f"  总耗时: {duration:.2f} 秒")
        if articles:
            print(f"  平均速度: {len(articles)/duration:.2f} 文章/秒")
        
        if articles:
            print(f"  文章示例:")
            for i, article in enumerate(articles[:5], 1):
                print(f"    {i}. {article.get('title', 'N/A')[:50]}...")
                print(f"       URL: {article.get('url', 'N/A')}")
        
        return len(articles), duration
        
    except Exception as e:
        print(f"❌ 深度爬取测试失败: {e}")
        return 0, 0

async def main():
    """主测试函数"""
    print("开始动态加载功能测试")
    print("目标: 验证'加载更多'按钮处理和链接提取效果")
    print()
    
    # 运行测试
    tests = [
        ("基础 vs 增强型提取对比", test_link_extraction_comparison),
        ("不同加载更多次数效果", test_different_load_more_counts),
        ("带动态加载的深度爬取", test_mini_deep_crawl_with_dynamic),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            print(f"\n{'='*20} {test_name} {'='*20}")
            result = await test_func()
            results.append((test_name, True, result))
        except Exception as e:
            print(f"❌ 测试 {test_name} 异常: {e}")
            results.append((test_name, False, None))
    
    # 输出总结
    print("\n" + "=" * 60)
    print("动态加载功能测试总结")
    print("=" * 60)
    
    passed = sum(1 for _, success, _ in results if success)
    total = len(results)
    
    for test_name, success, result in results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"{test_name}: {status}")
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 动态加载功能测试通过！")
        print()
        print("📝 使用建议:")
        print("  # 标准深度爬取（支持动态加载）")
        print("  python main_advanced.py --deep-crawl")
        print("  ")
        print("  # 增加加载更多次数获取更多链接")
        print("  python main_advanced.py --deep-crawl --load-more-clicks 15")
        print("  ")
        print("  # 快速模式 + 更多加载")
        print("  python main_advanced.py --deep-crawl --fast-mode --load-more-clicks 20")
    else:
        print("⚠️  部分测试失败，请检查网络连接和页面结构")

if __name__ == "__main__":
    asyncio.run(main())

#!/usr/bin/env python3
"""
快速测试Media模型修改
"""

print("开始测试...")

try:
    print("1. 测试SQLAlchemy导入...")
    from sqlalchemy import create_engine, Column, String, Integer, Text, Boolean
    print("✅ SQLAlchemy导入成功")
except ImportError as e:
    print(f"❌ SQLAlchemy导入失败: {e}")
    exit(1)

try:
    print("2. 测试database模块导入...")
    import database
    print("✅ database模块导入成功")
except ImportError as e:
    print(f"❌ database模块导入失败: {e}")
    exit(1)

try:
    print("3. 测试Media模型...")
    media_model = database.Media
    print(f"✅ Media模型存在: {media_model.__tablename__}")
    
    # 检查字段
    fields = ['id', 'mediaId', 'source', 'title', 'url', 'createTime', 'content', 'important']
    for field in fields:
        if hasattr(media_model, field):
            print(f"  ✅ 字段 {field}")
        else:
            print(f"  ❌ 缺少字段 {field}")
            
except Exception as e:
    print(f"❌ Media模型测试失败: {e}")
    exit(1)

try:
    print("4. 测试DatabaseManager...")
    db_manager = database.DatabaseManager()
    print("✅ DatabaseManager创建成功")
    
    # 测试数据转换
    test_data = {
        'title': '测试标题',
        'url': 'https://test.com',
        'content': '测试内容',
        'publish_time': '2024-01-01'
    }
    
    converted = db_manager._convert_to_db_model(test_data)
    if converted:
        print("✅ 数据转换成功")
        print(f"  转换后字段: {list(converted.keys())}")
    else:
        print("❌ 数据转换失败")
        
except Exception as e:
    print(f"❌ DatabaseManager测试失败: {e}")
    import traceback
    traceback.print_exc()
    exit(1)

print("\n🎉 所有基本测试通过！")
print("📝 aicrawl已成功修改为使用Media模型")
print("📝 主要变更:")
print("   - 移除了XueqiuArticle模型")
print("   - 添加了Media模型")
print("   - 调整了字段映射")
print("   - 移除了scrapy依赖")
